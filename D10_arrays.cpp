#include <iostream>
using namespace std;

// arrays are passed by reference example
// void arrayFunction(int arr[]);
// void arrayFunction2(int *ptr);

// --------------------------------------------------------------------------------------------------------------------------------------------

// printing an array from an external functionand not main function
// void printArr(int nums[], int n); 

// --------------------------------------------------------------------------------------------------------------------------------------------

// LINEAR SEARCH

// int linearSearch(int *arr, int n, int key);

// --------------------------------------------------------------------------------------------------------------------------------------------

// REVERSE AN ARRAY

// can be done using two methods : a) using extra space
//                                 b) without using any extyra space

// Method 1 : using extra space

// void printArr(int *arr, int n){
//     for(int i = 0; i < n; i++){
//         cout << arr[i] << ",";
//     }
//     cout << endl;
// }

// --------------------------------------------------------------------------------------------------------------------------------------------

// binarySearch

// int binarySearch(int *arr, int n, int key);

// --------------------------------------------------------------------------------------------------------------------------------------------




int main(){

    // DECLARATION OF ARRAY

    // memory is statically allocated (at compile time)

    // int marks[5]; // method 1
    
    // int marks[5] = {1,2,3,4,5}; // method 2

    // int marks2[] = {1,2,3}; // method 3

    // ------------------------------------------------------------------------------------------------------------------------

    // length of array

    // int n = sizeof(marks)/sizeof(int);

    // cout << "size of the array : " << n << endl;
    // cout << "elements of the array are as follows: " << endl;

    // ------------------------------------------------------------------------------------------------------------------------

    // output of elements in an array

    // for (int i = 0; i < n; i++){
    //     cout << marks[i] << endl;
    // }

    // -------------------------------------------------------------------------------------------------------------------------

    // input for the array

    // int size;

    // cout << "enter the size of the array ";
    // cin >> size;

    // int arr[size];

    // int arr_len = sizeof(arr)/sizeof(int);

    // for(int i = 0; i < arr_len; i++){
    //     cin >> arr[i];
    // }
    //     cout << endl;
        
    // for(int i = 0; i < arr_len; i++){
    //     cout << arr[i] << endl;
    // }

    // --------------------------------------------------------------------------------------------------------------------------

    // QUE : Find the largest and smallest in an array

    // int arr[] = {5,4,3,9,12};
    // int size = sizeof(arr)/sizeof(int);

    // int max = arr[0];
    // int min = arr[0];

    // for(int i = 0; i < size; i++){
    //     if(arr[i] > max){
    //         max = arr[i];
    //     }
    //     if(arr[i] < min){
    //         min = arr[i];
    //     }
    // }

    // cout << "largest " << max << endl;
    // cout << "smallest " << min;

    // -------------------------------------------------------------------------------------------------------------------------

    // IN C++ AN ARRAY NAME CAN BE CONVERTED INTO A POINTER.
    // ARRAYS ARE PASSED BY REFERENCE in an function

    // int arr[] = {1,2,3,4,5};
    // int a;
    // int *ptr = &a; 
    // cout << ptr << endl; //0x...   
    // int n = sizeof(arr) / sizeof(int);
    // cout << arr << endl; // address of the array in memory
    // cout << *arr << endl; //value at index 1 same as arr[0]
    // cout << *(arr+1) << endl; // value at index 2 same as arr[1]
    // cout << *(arr+2) << endl;

    // arrayFunction(arr); // passing array name is equivalent to passing the pointer
    // cout << arr[0] << endl;

    // arrayFunction2(arr); // passing array name is equivalent to passing the pointer
    // cout << arr[0] << endl;

    // both function1 and functrion2 give same result as they are same just a different type of writing it.

    // ---------------------------------------------------------------------------------------------------------------------------

    // printing an array through a function 

    // int arr[] = {1,2,3,4,5};

    // int n = sizeof(arr) / sizeof(int);

    // printArr(arr, n);

    // ----------------------------------------------------------------------------------------------------------------------------

    // LINEAR SEARCH

    // int arr[] = {2,4,6,8,10,12,14,16};
    // int n = sizeof(arr)/sizeof(int);

    // cout << linearSearch(arr, n, 10);

    // --------------------------------------------------------------------------------------------------------------------------------------------

    // REVERSE AN ARRAY

    // can be done using two methods : a) using extra space
    //                                 b) without using any extyra space

    // Method 1 : using extra space || Time complexity : O(n) || space complexity : n

    // int arr[] = {5,4,3,9,2};
    // int n = sizeof(arr)/sizeof(int);
    
    // int copyArr[n];
    // for(int i = 0; i < n; i++){
    //     int j = n - i -1;
    //     copyArr[i] = arr[j];
    // }

    // for(int i = 0; i < n; i++){
    //     arr[i] = copyArr[i];
    // }

    // printArr(arr,n);

    // -------------------------------------------------------

    // Method 2 : without using extra space (2 pointer approach) || Time complexity : O(n) || space complexity : O(1)

    // int arr[] = {5,4,3,9,2};
    // int n = sizeof(arr)/sizeof(int);

    // int start = 0, end = n - 1;

    // while(start < end){

    //     // swaping two variables
    //     int temp = arr[start];
    //     arr[start] = arr[end];
    //     arr[end] = temp;

    //     // we can also swap the two arrays by using the inbuilt function in c++ calleed swap()
    //     // swap(arr[start], arr[end])

    //     start++;
    //     end--;

    // }

    // printArr(arr,n);

    // --------------------------------------------------------------------------------------------------------------------------------------------

    //  BINARY SEARCH (for sorted array / sorted values)

    // time complexity : O(log n) ; very good compared to O(n)

    // int arr[] = {5,4,3,9,2};
    // int n = sizeof(arr)/sizeof(int);

    // cout << binarySearch(arr, n , 2) << endl;

    // --------------------------------------------------------------------------------------------------------------------------------------------

    // POINTER ARITHMATIC


    
    return 0;
}

// void arrayFunction(int arr[]){
//     arr[0] = 2000;   
// }

// void arrayFunction2(int *ptr){
//     ptr[0] = 1000;
// }

// -------------------------------------------------------------------------------------------------------------------------------------------

// void printArr(int nums[], int n){ 
//     for(int i = 0; i < n; i++){
//         cout << nums[i] << " ";
//     }
//     cout << endl;
// }

// --------------------------------------------------------------------------------------------------------------------------------------------

// LINEAR SEARCH

// int linearSearch(int *arr, int n, int key){ 
//     // time complexity for the below implementation is O(n)
//     for(int i = 0; i < n; i++) {
//         if(arr[i] == key){
//             return i;
//         }

//     }
//     return -1;
// }

// --------------------------------------------------------------------------------------------------------------------------------------------

// REVERSE AN ARRAY

// can be done using two methods : a) using extra space
//                                 b) without using any extyra space

// Method 1 : using extra space

// --------------------------------------------------------------------------------------------------------------------------------------------

// BINARY SEARCH 

// int binarySearch(int *arr, int n, int key){
//     int start = 0; int end = n - 1;

//     while(start <= end){
//         int mid = (start + end) / 2;

//         if(arr[mid] == key){
//             return mid;
//         } else if (arr[mid < key]){ // 2nd half
//             start = mid + 1;
            
//         }else { // 1st half 
//             end = mid - 1;
//         }
        
//     }
//     return -1;
// } 

// --------------------------------------------------------------------------------------------------------------------------------------------







