#include <iostream>
using namespace std;

int sum(int a, int b){
    int sum = a + b;

    return sum;
}

int fact(int n){
    int fact = 1;
    if(n == 0 || n == 1){
        return 1;
    }
    for(int i = n; i >= 1; i--){
        
        fact = fact * i;
        
    }
    return fact;
}

bool isPrime(int n){
    if (n == 1){
        return false;
    }

    for(int i = 2; i * i <= n; i++){
        if (n % i == 0){
            return false;
        } 
    }

    return true;

}

void allPrimes(int n){
    for (int i = 2; i <= n; i++){
        if(isPrime(i)){
            cout << i << " ";
        }
    }
    cout << endl;
}

int main(){

    // cout << sum(4,5);

    allPrimes(50);



    return 0;
}
