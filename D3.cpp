//constant keyword and difference between cons and macro

#include <iostream>
#define X 25 // Symbolic constans MACRO
using namespace std;

int main () {

    // Constants

    const int num = 2; // must be initilaised meaning we should give it some initial value
    int num2 = 3;

    // macro does not take any space in the memory intead X will be replaced by 25 during the runtime 
    cout << X;
    
    return 0;
}  