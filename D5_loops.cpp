// print sum of n natural numbers

#include <iostream>
#include <cmath>
using namespace std;

int main(){

    // FOR LOOP 

    // int n;
    // int sum = 0;
    // cout<< "enter your nuber till which yu want to sum.";
    // cin >> n;
    // cout << endl;

    // METHOD 2 : USING FOR LOOP 

    // for(int i = 1; i <= n; i++){
    //     sum += i;
        
    // }
    // cout << sum << endl; 

    // METHOD 2 : using formula 
    // sum = (n * (n + 1))/2;
    // cout << sum;

    // for(int i = 0; i < 4; i++){
    //     for(int j = 0; j < 4; j++){
    //         cout << "*";

    //     }
    //     cout << endl;
    // }



    // for(int i = 5; i > 0; i--){
    //     cout << i <<endl;
    // }


    // sum of n digits of a number.
    // int n;
    // int sum = 0;
    // cout << "enter the number" << endl; 
    // cin >> n;
    // while(n > 0){
        
    //     int digits = n % 10;
    //     sum = sum + digits;

    //     n = n / 10;

    // }
    // cout << sum;

    // PRINT THE SUM OF ODD DIGITS OF A NUMBER.

    // int n;
    // int sum = 0;
    // cout << "enter the number" << endl; 
    // cin >> n;
    // while(n > 0){
        
    //     int digits = n % 10;
    //     if(n % 2 != 0){
    //         sum = sum + digits;
    //     }
    //     n = n / 10;
    // }
    // cout << sum;

    // PRINT DIGITS OF GIVEN NUMBER IN REVERSE

    // int n;
    // int sum = 0;
    // cout << "enter the number" << endl; 
    // cin >> n;
    // while(n > 0){
        
    //     int digits = n % 10;
    //     cout << digits;
    //     n = n / 10;
    // }
    
    // Reverse a given number & print the result.

    // int n;
    // int result = 0;
    // cout << "enter the number" << endl; 
    // cin >> n;
    // while(n > 0){
        
    //     int digits = n % 10;
    //     result = 10 * result + digits;
    //     n = n / 10;
    // }
    // cout << "reverse = " << result << endl; 


    // CHECK IF A NO. IS PRIME OR NOT (not optimised)

    // int n;
    // cout << "enter no. to check if prime or not" << endl;
    // cin >> n;

    // int isPrime = true;

    // for(int i = 2; i <= n - 1; i++){
    //     if(n % i == 0) { // finding factors of a numbers to check if it prime or not
    //         isPrime = false;
    //         break;
    //     }
    // }

    // if (isPrime){  
    //         cout << "number is Prime";
    // }else {
    //         cout << "number is not prime";
    //     }

    // CHECK IF A NO. IS PRIME OR NOT (optimised)

    int n;
    cout << "enter no. to check if prime or not" << endl;
    cin >> n;

    int isPrime = true;

    for(int i = 2; i <= sqrt(n); i++){
        if(n % i == 0) { // finding factors of a numbers to check if it prime or not
            isPrime = false;
            break;
        }
    }

    if (isPrime){  
            cout << "number is Prime";
    }else {
            cout << "number is not prime";
        }


    return 0;
}