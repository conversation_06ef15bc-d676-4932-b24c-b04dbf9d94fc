#include <iostream>
using namespace std;

// for pass by value
void changeA(int a);

void changeA2(int *ptr);

void changeA3(int &a);


int main(){

    // int a = 10;
    // int *ptr = &a;
    // cout << sizeof(ptr) << endl;
    // cout << &a << " = " << ptr << endl;

    // -----------------------------------------------------------------------------------------------------------------------

    //taking pointer of pointer

    // int **pptr = &ptr;

    // cout << pptr << " = " << &ptr << endl;

    // ------------------------------------------------------------------------------------------------------------------------

    // Dereferencing operator = *

    // int a = 10;
    // int *ptr = &a;

    // cout << ptr << "\n";
    // cout << *ptr << endl; // derefrencing occurs here

    // ------------------------------------------------------------------------------------------------------------------------

    // NULL POINTER

    // int *ptr = NULL;
    // cout << ptr;

    // null pointer can not be dereferenced

    // cout << *ptr << endl; // will give segmentation error
    
    // ----------------------------------------------------------------------------------------------------------------

    // PASSING ARGUMENTS
    
    // 1. Pass by Value
    
    // int a = 10;
    // changeA(a);        
    
    // cout << a << endl;
    
    // 2. Pass by reference using pointers

    // int a = 10;
    // changeA2(&a);
    
    // cout << a << endl; 

    // ------------------------------------------------------------------------------------------------------------------------

    // REFERENCE VARIABLE

    int a = 10;

    int &b = a;
    b = 25;

    cout << a << " in main 'a' before calling changeA3 function" << endl;

    
    changeA3(a);

    cout << a << " in main a " << endl;
    cout << b << " in main b" << endl;

    // ------------------------------------------------------------------------------------------------------------------------

    // float* a,b;

    // here above : a is an float ptr
    //              b is an normal float variable       

    // ALSO WE CAN INITIALISE THE POINTER WITH 0. IT WILL BE SAME AS NULL POINTER.

    return 0;
}

// 1. Pass by Value

void changeA(int a){
    a = 20;
    cout << a << endl;
}

// 2.a Pass by reference using pointers

void changeA2(int *ptr){
    *ptr = 20; // Dereferencing 
    cout << *ptr << endl;
}

// 2.b Pass by Reference using reference variables

void changeA3(int &a){
    a = 20; // reference varibale changing the value at the memory location of var a 
    cout << a << " in changeA3 " << endl;
}


