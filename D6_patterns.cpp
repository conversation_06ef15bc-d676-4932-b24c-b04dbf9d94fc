#include <iostream>
using namespace std;

int main(){
    // int n = 5;
    // for(int i = 1; i <= n; i++){
    //     int val = i;
    //     for(int j = 1; j <= n; j++){
    //         cout << val << " ";
    //     }
    //     cout << endl;
    // }

    // inverted star pattern 

    // my approach without any help
    // int n = 4;
    // for(int i = 1; i <= n; i++){
    //     for(int j = n; j >= i; j--){
    //         cout << "*";
    //     }
    //     cout << endl;
    // }


    // <PERSON><PERSON><PERSON><PERSON>'s approach 

    // int n = 4;
    // for(int i = 1; i <= n; i++){
    //     for(int j = 1; j <= (n - i + 1); j++ ){
    //         cout << "*";
    //     }
    //     cout << endl;
    // }


    // Half pyramid

    // int n = 4;

    // for(int i = 1; i <= n; i++){
    //     for(int j = 1; j <= i; j++){
    //         cout << j << " ";
    //     }
    //     cout << endl;
    // }

    // character pyramid pattern

    // int n = 4;
    // char ch = 'A';

    // for(int i = 1; i <= n; i++){

    //     for(int j = 1; j <= i; j++){
    //         cout << char(ch) << " ";
    //          ch++;
    //     }
       
    //     cout << endl;
    // }

    // Hollow Rectangle Pattern

    // int n = 4;

    // for(int i = 1; i <= n; i++){
    //    cout << "*";

    //    for(int j = 1; j <= n-1; j++){
    //     if(i == 1 || i == n){
    //         cout << "*";
    //     } else {
    //         cout << " ";
    //     }
    // }
    // cout << "*" << endl;
    // }

    // inverted and rotated half-pyramid 

    // int n = 5;

    // for (int i = 1; i < n; i++){
    //     for(int j = 1; j < n - i; j++){
    //         cout << " ";
    //     }
    //     for(int j = 1; j <= i; j++){
    //         cout << "*";
    //     }
    //     cout << endl;
    // }

    // Print floyd's triangle

    // int n = 5;

    // int num = 1;

    // for(int i = 1; i <= n; i++){
    //     for(int j = 1; j <= i; j++){
    //         cout << num << " ";
    //         num++;
    //     }
    //     cout << endl;
    // }

    // Diamond pattern problem

    // int n = 4;

    // my approach for upper pyramid

    // for(int i = 0; i < n; i++){

    //     for(int j = 0; j < n-i; j++){
    //         cout << " ";
    //     }

    //     for(int j = 0; j <= i; j++){
    //         cout << "*";
    //     }

    //     for(int j = 1; j <= i; j++){
    //         cout << "*";
    //     }
    //     cout << endl;
    // }

    // Shraddha's approach for upper pyramid

    // for(int i = 1; i <= n; i++) {
    //     for(int j = 1; j <= n-i; j++) {
    //         cout << " ";
    //     }

    //     for(int j = 1; j <= 2*i - 1; j++) {
    //         cout << "*";
    //     }

    //     cout << endl;
    // }
    // for(int i = n; i >= 1; i--) {
    //     for(int j = 1; j <= n-i; j++) {
    //         cout << " ";
    //     }

    //     for(int j = 1; j <= 2*i - 1; j++) {
    //         cout << "*";
    //     }

    //     cout << endl;
    // }

    // Print the 0-1 Triangle Pattern.

    // int n = 5;
    // int val = true;
    // for(int i = 1; i <= n; i++){
    //     for(int j = 1; j <= i; j++){
    //         cout << val << " ";
    //         val = !val;
    //     }
    //     cout << endl;
    // }

    // Print the RhombusPattern
    
    // int n = 5;

    // for(int i = 1; i <= n; i++){
    //     for(int j = 1; j <= n-i; j++){
    //         cout << " ";
    //     }
    //     for(int j = 1; j <= n; j++){
    //         cout << "*";
    //     }
    //     cout << endl;
    // }

    // Print the Palindromic Pattern with Numbers in a traiangle pattern

    // int n = 5;

    // for(int i = 1; i <= n; i++){
    //     for(int j = 1; j <= n-i; j++){
    //         cout << " ";
    //     }
    //     for(int j = i; j >= 1; j--) {
    //         cout << j;
    //     }
    //     for(int j = 2; j <= i; j++) {
    //         cout << j;
    //     }
    //     cout << endl;
    // }
    


    return 0;
}