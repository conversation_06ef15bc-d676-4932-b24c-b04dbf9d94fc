// TYPECASTING

// Conversion of data from one type to another.
// Implicit Conversion (automatic/ type promotion)
// Done by compiler to avoid data loss.
// bool -> char -> int -> float -> double

#include <iostream>
using namespace std;

int main(){

    // Implicit Typecasting
    // cout << (10/3) << endl;
    // cout << (10/3.0) << endl;

    // cout << ('A' + 1);

    // EXPLICIT TYPECASTING

    float PI = 3.14;
    cout << (int)(PI) << endl; // 3.14 -> 3
    cout << ((float)10/3) << endl; // 3 -> 3.333
    cout << (char)('A' + 1); // int to char output : B

    return 0;
}