#include <iostream>
using namespace std;

void maxprofit(int *prices, int n);

int main(){

    // Printing sub arrays.

    // Subarray is continuous part of an array

    // total number of subarrays in an array is equal to ( n * (n+1) )/ 2. sum of n natural numbers
    
    // time complexity of this approach is : O(n^3)

    // int arr[] = {1,2,3,4,5};
    // int size = sizeof(arr)/sizeof(int);
    // int idx = 0;

    // for(int i = 0; i < size; i++){
    //  for(int j = i; j < size; j++){
    //      for(int k = i; k <= j; k++){
    //          cout << arr[k];
    //      }
    //      cout << ", ";

    //  }
    //  cout << endl;
    // }

    // ------------------------------------------------------------------------------------

    // Maximum Subarray Sum

    // Method 1 of 3 : Brute force approach

    // int arr[] = {2,-3,6,-5,4,2};
    // int size = sizeof(arr)/sizeof(int);
    // int finalSum = INT_MIN;    
    // // calculating the subarray

    // for(int i = 0; i < size; i++){
    //     for(int j = i; j < size; j++){
    //         int sum = 0; // important declaration it should be declared here only.
    //         for(int k = i; k <= j; k++){
    //             sum = sum + arr[k];
    //         }
    //         cout << sum << ", ";
    //         finalSum = max(finalSum, sum);
    //     }
    //     cout << endl;
        
    // }
    // cout << "maximum sum is as follows of the sub arrays : " << finalSum;

    // ------------------------------------------------------------------------------------------------------------------------

    // Method 2 : Slightly optimised (most inner loop) 
    // time complexity : O(n^2)

    // int arr[] = {2,-3,6,-5,4,2};
    // int size = sizeof(arr)/sizeof(int);
    // int finalSum = INT_MIN;    
    // // calculating the subarray

    // for(int i = 0; i < size; i++){
    //     int sum = 0; // important declaration it should be declared here only.
    //     for(int j = i; j < size; j++){
    //         sum += arr[j]; 
    //         finalSum = max(finalSum, sum);
    //     }
        
    // }
    // cout << "maximum sum is as follows of the sub arrays : " << finalSum;

    // --------------------------------------------------------------------------------------------------------------------------

    // Kadane's algorithm (most optimised)

    // int arr[] = {2,-3,6,-5,4,2};
    // int size = sizeof(arr)/sizeof(int);
    // int sum = 0; // in this we can declare it here outside the loop
    // int finalSum = INT_MIN;    

    // for(int i = 0; i < size; i++){
    //     sum += arr[i];
    //     finalSum = max(sum, finalSum);
    //     if(sum < 0){
    //         sum = 0;
    //     }
        
    // }
    // cout << "maximum sum is as follows of the sub arrays : " << finalSum;

    // --------------------------------------------------------------------------------------------------------------------------

    // Best Time to Buy and Sell Stock
    
    // int prices[6] = {7,1,5,3,6,4};
    // int n = sizeof(prices) / sizeof(int);

    // maxprofit(prices, n);

    


    return 0;
}

void maxprofit(int *prices, int n){
    int bestBuy[100000];
    bestBuy[0] = INT_MAX;

    for(int i = 1; i<=n; i++){
        bestBuy[i] = min(bestBuy[i-1], prices[i-1]);
        // printing best buy's
        // cout << bestBuy[i] << ", ";
    }

    int maxProfit = 0;
    
    for(int i = 0; i < n; i++){
        int currentProfit = prices[i] - bestBuy[i];
        maxProfit = max(maxProfit, currentProfit);
    }

    cout << maxProfit << endl;
    // time complexity O(n+n) = O(2n) = O(n)
    

    
    


}